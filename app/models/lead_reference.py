"""
Lead reference models for database operations
"""

import uuid
from sqlalchemy import Column, String, Boolean, DateTime, func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from app.core.database.connection import Base


class LeadSource(Base):
    """Lead source reference model"""
    
    __tablename__ = "lead_sources"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    name = Column(String(100), nullable=False, unique=True, index=True)
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    leads = relationship("Lead", back_populates="lead_source_rel", foreign_keys="Lead.lead_source_id")
    
    def __repr__(self):
        return f"<LeadSource(id={self.id}, name={self.name}, is_active={self.is_active})>"


class LeadStatus(Base):
    """Lead status reference model"""
    
    __tablename__ = "lead_statuses"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    name = Column(String(100), nullable=False, unique=True, index=True)
    colour = Column(String(7), nullable=False)  # Hex color code
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    leads = relationship("Lead", back_populates="lead_status_rel", foreign_keys="Lead.lead_status_id")
    
    def __repr__(self):
        return f"<LeadStatus(id={self.id}, name={self.name}, colour={self.colour}, is_active={self.is_active})>"


class QuestionBank(Base):
    """Question bank model for prequalification questions"""
    
    __tablename__ = "question_bank"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    name = Column(String, nullable=False)
    lead_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    franchisor_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    def __repr__(self):
        return f"<QuestionBank(id={self.id}, name={self.name}, is_active={self.is_active})>"


class EscalationQuestionBank(Base):
    """Escalation question bank model"""
    
    __tablename__ = "escalation_question_bank"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    name = Column(String, nullable=False)
    lead_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    franchisor_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    answer = Column(String)  # JSON array of strings
    support_status = Column(String)  # Free text field
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    def __repr__(self):
        return f"<EscalationQuestionBank(id={self.id}, name={self.name}, support_status={self.support_status})>"
