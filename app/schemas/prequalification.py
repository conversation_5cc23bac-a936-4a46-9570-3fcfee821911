"""
Prequalification Question Bank schemas
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
import uuid
from app.schemas.base_response import ResponseMessage


class QuestionBankResponse(BaseModel):
    """Response model for question bank"""
    id: str = Field(..., description="Question bank ID", example="550e8400-e29b-41d4-a716-************")
    name: str = Field(..., description="Question name", example="What is your investment budget?")
    lead_id: Optional[str] = Field(None, description="Associated lead ID")
    franchisor_id: Optional[str] = Field(None, description="Associated franchisor ID")
    is_deleted: bool = Field(..., description="Whether the question is deleted")
    is_active: bool = Field(..., description="Whether the question is active")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    class Config:
        from_attributes = True


class QuestionBankListResponse(BaseModel):
    """Response model for question bank list"""
    items: List[QuestionBankResponse] = Field(..., description="List of question bank items")
    total_count: int = Field(..., description="Total number of items")


class QuestionBankSuccessResponse(BaseModel):
    """Standard success response for question bank operations"""
    success: bool = Field(True, description="Operation success status")
    status: str = Field("success", description="Response status")
    message: ResponseMessage = Field(..., description="Response message")
    data: QuestionBankListResponse = Field(..., description="Question bank data")


class EscalationQuestionBankResponse(BaseModel):
    """Response model for escalation question bank"""
    id: str = Field(..., description="Escalation question bank ID", example="550e8400-e29b-41d4-a716-************")
    name: str = Field(..., description="Question name", example="Budget inquiry escalation")
    lead_id: Optional[str] = Field(None, description="Associated lead ID")
    franchisor_id: Optional[str] = Field(None, description="Associated franchisor ID")
    answer: Optional[List[str]] = Field(None, description="Array of answer strings", example=["$50,000-$100,000", "$100,000-$200,000", "$200,000+"])
    support_status: Optional[str] = Field(None, description="Support status", example="Pending review")
    is_deleted: bool = Field(..., description="Whether the question is deleted")
    is_active: bool = Field(..., description="Whether the question is active")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    class Config:
        from_attributes = True


class EscalationQuestionBankListResponse(BaseModel):
    """Response model for escalation question bank list"""
    items: List[EscalationQuestionBankResponse] = Field(..., description="List of escalation question bank items")
    total_count: int = Field(..., description="Total number of items")


class EscalationQuestionBankSuccessResponse(BaseModel):
    """Standard success response for escalation question bank operations"""
    success: bool = Field(True, description="Operation success status")
    status: str = Field("success", description="Response status")
    message: ResponseMessage = Field(..., description="Response message")
    data: EscalationQuestionBankListResponse = Field(..., description="Escalation question bank data")


class EscalationAnswerUpdateRequest(BaseModel):
    """Request model for updating escalation question answer"""
    answer: List[str] = Field(..., description="Array of answer strings", example=["$50,000-$100,000", "$100,000-$200,000", "$200,000+"])

    class Config:
        from_attributes = True


class EscalationStatusUpdateRequest(BaseModel):
    """Request model for updating escalation question support status"""
    support_status: str = Field(..., description="Support status", example="Resolved")

    class Config:
        from_attributes = True


class EscalationUpdateSuccessResponse(BaseModel):
    """Standard success response for escalation question update operations"""
    success: bool = Field(True, description="Operation success status")
    status: str = Field("success", description="Response status")
    message: ResponseMessage = Field(..., description="Response message")
    data: EscalationQuestionBankResponse = Field(..., description="Updated escalation question data")
