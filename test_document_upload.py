#!/usr/bin/env python3
"""
Test script to verify document upload functionality after API removal
"""

import requests
import io
import os
from typing import Dict, Any

class DocumentUploadTester:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.auth_token = None
        
    def authenticate(self, email: str = "<EMAIL>", password: str = "admin123") -> bool:
        """Authenticate and get token"""
        try:
            response = requests.post(
                f"{self.base_url}/api/auth/login",
                json={"email_or_mobile": email, "password": password}
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success") and data.get("data", {}).get("access_token"):
                    self.auth_token = data["data"]["access_token"]
                    print("✅ Authentication successful")
                    return True
            
            print(f"❌ Authentication failed: {response.status_code}")
            return False
            
        except Exception as e:
            print(f"❌ Authentication error: {e}")
            return False
    
    def create_test_pdf(self) -> bytes:
        """Create a simple test PDF content"""
        # Simple PDF header - this is a minimal PDF structure
        pdf_content = b"""%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
72 720 Td
(Test Document) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
299
%%EOF"""
        return pdf_content
    
    def test_pdf_upload(self) -> bool:
        """Test uploading a PDF file"""
        if not self.auth_token:
            print("❌ No auth token available")
            return False
            
        try:
            # Create test PDF
            pdf_content = self.create_test_pdf()
            
            # Prepare form data
            files = {
                'file': ('test_document.pdf', io.BytesIO(pdf_content), 'application/pdf')
            }
            data = {
                'name': 'Test PDF Document',
                'description': 'Test PDF upload after API cleanup',
                'is_active': True
            }
            
            headers = {
                'Authorization': f'Bearer {self.auth_token}'
            }
            
            response = requests.post(
                f"{self.base_url}/api/documents/upload",
                files=files,
                data=data,
                headers=headers
            )
            
            if response.status_code == 201:
                result = response.json()
                if result.get("success"):
                    print("✅ PDF upload successful")
                    print(f"   Document ID: {result.get('data', {}).get('id')}")
                    return True
            
            print(f"❌ PDF upload failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
        except Exception as e:
            print(f"❌ PDF upload error: {e}")
            return False
    
    def test_non_pdf_upload(self) -> bool:
        """Test uploading a non-PDF file (should fail)"""
        if not self.auth_token:
            print("❌ No auth token available")
            return False
            
        try:
            # Create test text file
            text_content = b"This is a test text file, not a PDF"
            
            # Prepare form data
            files = {
                'file': ('test_document.txt', io.BytesIO(text_content), 'text/plain')
            }
            data = {
                'name': 'Test Text Document',
                'description': 'Test non-PDF upload (should fail)',
                'is_active': True
            }
            
            headers = {
                'Authorization': f'Bearer {self.auth_token}'
            }
            
            response = requests.post(
                f"{self.base_url}/api/documents/upload",
                files=files,
                data=data,
                headers=headers
            )
            
            if response.status_code == 400:
                result = response.json()
                if not result.get("success") and "PDF" in result.get("message", {}).get("description", ""):
                    print("✅ Non-PDF upload correctly rejected")
                    return True
            
            print(f"❌ Non-PDF upload should have been rejected but got: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
        except Exception as e:
            print(f"❌ Non-PDF upload test error: {e}")
            return False
    
    def test_removed_endpoints(self) -> bool:
        """Test that removed endpoints return 404"""
        if not self.auth_token:
            print("❌ No auth token available")
            return False
            
        headers = {
            'Authorization': f'Bearer {self.auth_token}',
            'Content-Type': 'application/json'
        }
        
        removed_endpoints = [
            ("POST", "/api/documents/", {"name": "test", "file_path": "test.pdf"}),
            ("POST", "/api/documents/bulk-upload", {}),
            ("PUT", "/api/documents/test-id", {"name": "updated"}),
            ("POST", "/api/documents/bulk-delete", {"document_ids": ["test-id"]})
        ]
        
        all_removed = True
        
        for method, endpoint, data in removed_endpoints:
            try:
                if method == "POST":
                    response = requests.post(f"{self.base_url}{endpoint}", json=data, headers=headers)
                elif method == "PUT":
                    response = requests.put(f"{self.base_url}{endpoint}", json=data, headers=headers)
                
                if response.status_code in [404, 405]:  # 404 Not Found or 405 Method Not Allowed
                    print(f"✅ {method} {endpoint} correctly removed (status: {response.status_code})")
                else:
                    print(f"❌ {method} {endpoint} still accessible (status: {response.status_code})")
                    all_removed = False
                    
            except Exception as e:
                print(f"❌ Error testing {method} {endpoint}: {e}")
                all_removed = False
        
        return all_removed
    
    def run_all_tests(self) -> bool:
        """Run all tests"""
        print("🧪 Starting Document Upload Tests")
        print("=" * 50)
        
        # Authenticate
        if not self.authenticate():
            return False
        
        # Run tests
        tests = [
            ("PDF Upload Test", self.test_pdf_upload),
            ("Non-PDF Rejection Test", self.test_non_pdf_upload),
            ("Removed Endpoints Test", self.test_removed_endpoints)
        ]
        
        results = []
        for test_name, test_func in tests:
            print(f"\n🔍 Running {test_name}...")
            result = test_func()
            results.append(result)
            
        # Summary
        print("\n" + "=" * 50)
        print("📊 Test Summary:")
        passed = sum(results)
        total = len(results)
        print(f"   Passed: {passed}/{total}")
        
        if passed == total:
            print("🎉 All tests passed!")
            return True
        else:
            print("❌ Some tests failed!")
            return False

if __name__ == "__main__":
    tester = DocumentUploadTester()
    success = tester.run_all_tests()
    exit(0 if success else 1)
